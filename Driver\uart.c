/* uart.c */

#include "uart.h"
#include <stdio.h>
#include <stdarg.h>
#include <string.h>
#include <math.h>

// --- 全局变量定义 ---
IMUData_Packet_t IMUData_Packet;
AHRSData_Packet_t AHRSData_Packet;
u8 ttl_receive;
u8 Fd_data[64];
u8 Fd_rsimu[64];
u8 Fd_rsahrs[56];
int rs_imutype = 0;
int rs_ahrstype = 0;
extern int Time_count;

uint16_t uart_rx_index = 0;
uint32_t uart_rx_ticks = 0;
uint8_t uart_rx_buffer[128] = {0};

uint16_t uart2_rx_index = 0;
uint32_t uart2_rx_ticks = 0;
uint8_t uart2_rx_buffer[128] = {0};

u8 Usart_Receive;

// --- 中断回调函数 ---
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART1 || huart->Instance == USART2)
    {
        // 其他串口逻辑保持不变
    }
	
    /*****************************************************************/
    /* 关键修正：重写UART5处理逻辑，增强状态机的健壮性           */
    /*****************************************************************/
	if (huart->Instance == UART5)
    {
		static u8 Count = 0;
		static u8 last_rsnum = 0;
		static u8 rsimu_flag = 0;
		static u8 rsacc_flag = 0;

        // 1. 帧同步与组装
		if (((last_rsnum == FRAME_END) && (Usart_Receive == FRAME_HEAD)) || Count > 0)
		{
            if(Count < sizeof(Fd_data)) {
			    Fd_data[Count] = Usart_Receive;
            }
			Count++;
			
            // 提前识别帧类型
			if (Count >= 3) {
				if ((Fd_data[1] == TYPE_IMU) && (Fd_data[2] == IMU_LEN)) rsimu_flag = 1;
				if ((Fd_data[1] == TYPE_AHRS) && (Fd_data[2] == AHRS_LEN)) rsacc_flag = 1;
			}
		}
		else
		{
			Count = 0; // 无效字节，复位计数器
		}
		last_rsnum = Usart_Receive;

        // 2. 完整帧处理与状态机复位
        uint8_t frame_processed = 0; // 标记本轮中断是否处理了一帧

		if (rsimu_flag == 1 && Count == IMU_RS)
		{
			if (Fd_data[IMU_RS - 1] == FRAME_END) {
				memcpy(Fd_rsimu, Fd_data, IMU_RS);
				rs_imutype = 1;
			}
            frame_processed = 1;
		}
		else if (rsacc_flag == 1 && Count == AHRS_RS)
		{
			if (Fd_data[AHRS_RS - 1] == FRAME_END) {
				memcpy(Fd_rsahrs, Fd_data, AHRS_RS);
				rs_ahrstype = 1;
			}
            frame_processed = 1;
		}
        // 【健壮性保护】如果接收字节数超过最大可能帧长，说明同步丢失，强制复位
        else if (Count >= IMU_RS)
        {
            frame_processed = 1;
        }
		
        // 【关键】只要处理完一帧（无论成功失败）或发生超长，就必须重置状态机
        if(frame_processed)
        {
            Count = 0;
            rsimu_flag = 0;
            rsacc_flag = 0;
            memset(Fd_data, 0, sizeof(Fd_data));
        }

		// 3. 始终重新使能下一次中断
		HAL_UART_Receive_IT(&huart5, &Usart_Receive, 1);
	}
}

// --- 数据解析函数 ---
void TTL_Hex2Dec(void) // 【修正】返回值改为void
{
	if(rs_ahrstype == 1)
	{
		if(Fd_rsahrs[1] == TYPE_AHRS && Fd_rsahrs[2] == AHRS_LEN)
		{	
			AHRSData_Packet.RollSpeed = DATA_Trans(Fd_rsahrs[7],Fd_rsahrs[8],Fd_rsahrs[9],Fd_rsahrs[10]);
			AHRSData_Packet.PitchSpeed = DATA_Trans(Fd_rsahrs[11],Fd_rsahrs[12],Fd_rsahrs[13],Fd_rsahrs[14]);
			AHRSData_Packet.HeadingSpeed = DATA_Trans(Fd_rsahrs[15],Fd_rsahrs[16],Fd_rsahrs[17],Fd_rsahrs[18]);
			AHRSData_Packet.Roll = DATA_Trans(Fd_rsahrs[19],Fd_rsahrs[20],Fd_rsahrs[21],Fd_rsahrs[22]);
			AHRSData_Packet.Pitch = DATA_Trans(Fd_rsahrs[23],Fd_rsahrs[24],Fd_rsahrs[25],Fd_rsahrs[26]);
			AHRSData_Packet.Heading = DATA_Trans(Fd_rsahrs[27],Fd_rsahrs[28],Fd_rsahrs[29],Fd_rsahrs[30]);
			AHRSData_Packet.Qw = DATA_Trans(Fd_rsahrs[31],Fd_rsahrs[32],Fd_rsahrs[33],Fd_rsahrs[34]);
			AHRSData_Packet.Qx = DATA_Trans(Fd_rsahrs[35],Fd_rsahrs[36],Fd_rsahrs[37],Fd_rsahrs[38]);
			AHRSData_Packet.Qy = DATA_Trans(Fd_rsahrs[39],Fd_rsahrs[40],Fd_rsahrs[41],Fd_rsahrs[42]);
			AHRSData_Packet.Qz = DATA_Trans(Fd_rsahrs[43],Fd_rsahrs[44],Fd_rsahrs[45],Fd_rsahrs[46]);
		}
		rs_ahrstype = 0; // 清除标志位
	}
	
	if(rs_imutype == 1)
	{
		// ... IMU解析逻辑 ...
		rs_imutype = 0; // 清除标志位
	}
}

// ... 其他函数 (DATA_Trans, uart_proc, my_printf) 保持不变 ...
float DATA_Trans(u8 Data_1,u8 Data_2,u8 Data_3,u8 Data_4)
{
  long long transition_32;
	float tmp=0;
	int sign=0;
	int exponent=0;
	float mantissa=0;
  transition_32 = 0;
  transition_32 |=  Data_4<<24;
  transition_32 |=  Data_3<<16;
	transition_32 |=  Data_2<<8;
	transition_32 |=  Data_1;
  sign = (transition_32 & 0x80000000) ? -1 : 1;
	exponent = ((transition_32 >> 23) & 0xff) - 127;
	mantissa = 1 + ((float)(transition_32 & 0x7fffff) / 0x7fffff);
	tmp = sign * mantissa * pow(2, exponent);
	return tmp;
}

void uart_proc(void)
{
    if (uart_rx_index > 0 && uwTick - uart_rx_ticks > 100)
    {
        my_printf(&huart1, "uart data: %s\n", uart_rx_buffer);
        memset(uart_rx_buffer, 0, uart_rx_index);
        uart_rx_index = 0;
        huart1.pRxBuffPtr = uart_rx_buffer;
    }

    if (uart2_rx_index > 0 && uwTick - uart_rx_ticks > 100)
    {
        my_printf(&huart1, "uart2 data: %s\n", uart2_rx_buffer);
        memset(uart2_rx_buffer, 0, uart2_rx_index);
        uart2_rx_index = 0;
		huart2.pRxBuffPtr = uart2_rx_buffer;
    }
}

int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
    char buffer[512];
    va_list arg;
    int len;

    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);
    HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
    return len;
}
